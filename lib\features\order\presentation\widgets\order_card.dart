import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:restaurant_kitchen_app/config/constant/sized_box.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/features/order/data/model/order_model.dart';

class OrderCard extends StatelessWidget {
  final OrderModel order;
  final List<String> tables;
  const OrderCard({super.key, required this.order, required this.tables});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.orange.shade200, width: 1.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(order.id),
                  Row(
                    spacing: 4.w,
                    children: [
                      Image.asset(
                        'assets/images/chair.png',
                        height: 20.h,
                        width: 20.w,
                      ),
                      Text(
                        tables.join(','),
                        style: TextStyles.textFiedlLabelTextStyles.copyWith(
                          fontSize: 11.sp,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    DateFormat(
                      'dd MMM yyyy',
                    ).format(DateTime.parse(order.date)),
                    style: TextStyles.textFiedlLabelTextStyles.copyWith(
                      fontSize: 10.sp,
                    ),
                  ),
                  Text(
                    DateFormat('hh:mm a').format(DateTime.parse(order.date)),
                    style: TextStyles.textFiedlLabelTextStyles.copyWith(
                      fontSize: 10.sp,
                    ),
                  ),
                ],
              ),
            ],
          ),
          AppSize.v10,
          ListView.separated(
            separatorBuilder: (context, index) => AppSize.v8,
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: order.menuItems.length,
            itemBuilder: (context, itemIndex) {
              final item = order.menuItems[itemIndex];
              return Container(
                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 8.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey.shade300, width: 1.w),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          item.item.name,
                          style: TextStyles.bodyTextStyles.copyWith(
                            fontSize: 12.sp,
                          ),
                        ),
                        Text(
                          ' x${item.quantity}',
                          style: TextStyles.titleTextStyles.copyWith(
                            fontSize: 11.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                    Text(item.status),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
