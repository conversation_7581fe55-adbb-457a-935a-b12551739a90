import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:restaurant_kitchen_app/features/order/data/model/order_model.dart';
import 'package:restaurant_kitchen_app/features/order/domain/repo/i_order_repo.dart';
import 'package:restaurant_kitchen_app/network/api.dart';
import 'package:restaurant_kitchen_app/network/dio_client.dart';

class OrderRepoImpl implements OrderRepo {
  static final DioClient _dioClient = DioClient();
  @override
  Future<Either<String, List<OrderModel>>> getOrders({
    required String status,
    required String date,
  }) async {
    try {
      final res = await _dioClient.dio.get(
        Api.createOrder,
        queryParameters: {'date': date, 'status': status},
      );
      final data =
          (res.data['data'] as List)
              .map((e) => OrderModel.fromJson(e))
              .toList();
      return Right(data);
    } on DioException catch (e) {
      if (e.response != null) {
        debugPrint(
          'Error in getting order data ${e.response!.statusCode} : ${e.response!.statusMessage}',
        );
        debugPrint('${e.response!.data['message']}');
        return Left('${e.response!.data['message']}');
      } else {
        debugPrint('Error in getting order data: ${e.message}');
        return Left('${e.message}');
      }
    }
  }
}
