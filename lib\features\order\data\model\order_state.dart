import 'package:restaurant_kitchen_app/features/order/data/model/order_model.dart';

class OrderState {
  final bool isError;
  final String errMessage;
  final bool isSuccess;
  final bool isLoad;
  List<OrderModel> orderList;

  OrderState({
    required this.errMessage,
    required this.isError,
    required this.isLoad,
    required this.isSuccess,
    required this.orderList,
  });

  OrderState copyWith({
    bool? isError,
    String? errMessage,
    bool? isSuccess,
    bool? isLoad,
    List<OrderModel>? orderList,
  }) {
    return OrderState(
      errMessage: errMessage ?? this.errMessage,
      isError: isError ?? this.isError,
      isLoad: isLoad ?? this.isLoad,
      isSuccess: isSuccess ?? this.isSuccess,
      orderList: orderList ?? this.orderList,
    );
  }
}
