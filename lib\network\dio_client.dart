import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:restaurant_kitchen_app/features/auth/data/model/user.dart';
import 'package:restaurant_kitchen_app/network/api.dart';

class DioClient {
  late Dio dio;

  DioClient() {
    dio = Dio(
      BaseOptions(
        baseUrl: Api.baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {'Content-Type': 'application/json'},
      ),
    );

    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final accessToken = await _getAccessToken();
          if (accessToken != null) {
            options.headers['Authorization'] = 'Bearer $accessToken';
          }
          log('Access Token Is Valid');
          return handler.next(options);
        },
        onError: (DioException error, handler) async {
          if (_shouldRefreshToken(error)) {
            return await _refreshTokenAndRetry(error, handler);
          }
          log('Access Token Is Not Valid');
          return handler.next(error);
        },
      ),
    );
  }

  Future<String?> _getAccessToken() async {
    final box = Hive.box<User>('userBox');
    return box.isNotEmpty ? box.getAt(0)?.token : null;
  }

  Future<String?> _getRefreshToken() async {
    final box = Hive.box<User>('userBox');
    return box.isNotEmpty ? box.getAt(0)?.refreshToken : null;
  }

  bool _shouldRefreshToken(DioException error) {
    return error.response?.statusCode == 401;
  }

  Future<void> _refreshTokenAndRetry(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    final refreshToken = await _getRefreshToken();
    if (refreshToken == null) {
      return handler.reject(error);
    }

    try {
      final response = await dio.post(
        Api.refreshToken,
        data: {'refreshToken': refreshToken},
      );
      final newAccessToken = response.data['token'];
      final newRefreshToken = response.data['refreshToken'];

      // Update tokens in Hive
      final box = Hive.box<User>('userBox');
      if (box.isNotEmpty) {
        final user = box.getAt(0);
        final updatedUser = User(
          token: newAccessToken,
          refreshToken: newRefreshToken,
          userInfo: user!.userInfo,
        );
        await box.putAt(0, updatedUser);
      }

      // Retry the failed request
      final newRequest = error.requestOptions;
      newRequest.headers['Authorization'] = 'Bearer $newAccessToken';

      final retryResponse = await dio.fetch(newRequest);
      return handler.resolve(retryResponse);
    } catch (e) {
      return handler.reject(error);
    }
  }
}
