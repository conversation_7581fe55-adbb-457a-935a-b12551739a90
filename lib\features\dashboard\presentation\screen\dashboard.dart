import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/features/home/<USER>/screen/home_screen.dart';
import 'package:restaurant_kitchen_app/features/order/presentation/screen/order_screen.dart';
import 'package:restaurant_kitchen_app/features/profile/presentation/screen/profile_screen.dart';

final indexProvider = StateProvider.autoDispose<int>((ref) => 0);

// ignore: must_be_immutable
class Dashboard extends ConsumerWidget {
  Dashboard({super.key});
  DateTime? lastPressed;

  final List<Widget> screens = [HomeScreen(), OrderScreen(), ProfileScreen()];

  @override
  Widget build(BuildContext context, ref) {
    final currentIndex = ref.watch(indexProvider);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        final now = DateTime.now();

        if (lastPressed == null ||
            now.difference(lastPressed!) > const Duration(seconds: 2)) {
          lastPressed = now;
          // Show a SnackBar to prompt the user
          ScaffoldMessenger.of(context).clearSnackBars();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("Press back again to exit"),
              duration: Duration(seconds: 2),
              backgroundColor: kBrandColor,
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            ),
          );
        } else {
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        body: screens.elementAt(currentIndex),
        bottomNavigationBar: Theme(
          data: Theme.of(context).copyWith(
            splashColor: kBrandColor.withValues(alpha: 0.3),
            highlightColor: kBrandColor.withValues(alpha: 0.1),
          ),
          child: BottomNavigationBar(
            elevation: 4,
            currentIndex: currentIndex,
            onTap: (value) {
              ref.read(indexProvider.notifier).state = value;
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: kBrandColor,
            unselectedItemColor: Colors.grey,
            unselectedLabelStyle: TextStyles.bodyTextStyles.copyWith(
              fontSize: 11.sp,
            ),
            selectedLabelStyle: TextStyles.bodyTextStyles.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 11.sp,
            ),
            items: const [
              BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
              BottomNavigationBarItem(icon: Icon(Icons.list), label: 'Orders'),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
