// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';

class CustomInfoCard extends StatelessWidget {
  final VoidCallback? onTap;
  final String title;
  final String statusNo;
  final IconData imagePath;
  final Color? color;
  const CustomInfoCard({
    super.key,
    this.onTap,
    required this.title,
    required this.statusNo,
    required this.imagePath,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
              color: color ?? kBrandColor,
              borderRadius: BorderRadius.circular(12.r),
            ),
            alignment: Alignment.center,
            height: 109.h,
            width: 165.w,
            child: Text(
              statusNo,
              style: TextStyles.titleTextStyles.copyWith(
                fontSize: 24.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 11,
          left: 14,
          child: Row(
            spacing: 3.w,
            children: [
              Icon(imagePath, color: Colors.white, size: 16.sp),
              Text(
                title,
                style: TextStyles.latoTextStyles.copyWith(
                  color: Colors.white,
                  fontSize: 11.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
