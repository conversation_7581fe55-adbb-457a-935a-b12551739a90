import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:restaurant_kitchen_app/config/constant/sized_box.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/features/order/presentation/provider/order_provider.dart';
import 'package:restaurant_kitchen_app/features/order/presentation/widgets/order_card.dart';
import 'package:tuple/tuple.dart';

class OrderListHome extends ConsumerWidget {
  const OrderListHome({super.key});

  @override
  Widget build(BuildContext context, ref) {
    String date = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final orderState = ref.watch(orderListPorvider(Tuple2('inprogress', date)));
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 14.h),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.orange.shade200,
                borderRadius: BorderRadius.circular(6.r),
                border: Border.all(color: Colors.grey.shade300, width: 1.w),
              ),
              child: Text(
                '${orderState.orderList.length} Active Orders',
                style: TextStyles.titleTextStyles.copyWith(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                ref.invalidate(orderListPorvider(Tuple2('inprogress', date)));
              },
              child: ListView.separated(
                separatorBuilder: (context, index) => AppSize.v10,
                padding: EdgeInsets.symmetric(horizontal: 14.w),
                itemCount: orderState.orderList.length,
                itemBuilder: (context, index) {
                  final order = orderState.orderList[index];
                  final tables = order.tables.map((e) => e.name).toList();
                  return OrderCard(order: order, tables: tables);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
