import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

//----------------font styles--------------
class FontStyles {
  ///Lato
  static const lato = 'Lato';

  ///Poppins
  static const poppins = 'Poppins';

  ///OpenSans
  static const openSans = 'OpenSans';

  ///PublicSans
  static const publicSans = 'PublicSans-Regular';
  static const publicSansMedium = 'PublicSans-Medium';
}

//----------------text styles--------------

class TextStyles {
  static TextStyle openSansTextStyles = TextStyle(
    fontFamily: FontStyles.openSans,
    fontSize: 16.sp,
    fontWeight: FontWeight.w700,
  );
  static TextStyle bodyTextStyles = TextStyle(
    fontFamily: FontStyles.publicSans,
    fontSize: 14.sp,
  );

  static TextStyle poppinsTitleTextStyles = TextStyle(
    fontFamily: FontStyles.poppins,
    fontSize: 35.sp,
    fontWeight: FontWeight.w500,
  );
  static TextStyle latoTextStyles = TextStyle(
    fontFamily: FontStyles.lato,
    fontSize: 14.sp,
  );

  static TextStyle publicSansTextStyles = TextStyle(
    fontFamily: FontStyles.publicSans,
    fontSize: 14.sp,
  );
  static TextStyle textFiedlLabelTextStyles = TextStyle(
    fontFamily: FontStyles.publicSans,
    fontWeight: FontWeight.w600,
    fontSize: 14.sp,
  );
  static TextStyle titleTextStyles = TextStyle(
    fontFamily: FontStyles.publicSans,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );
  static TextStyle greetingStyle = TextStyle(
    fontFamily: FontStyles.publicSansMedium,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );
}
