import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

//----single image picker provider for profile---------
final imageProvider = StateNotifierProvider.autoDispose<ImageProvider, XFile?>(
  (ref) => ImageProvider(null),
);

class ImageProvider extends StateNotifier<XFile?> {
  ImageProvider(super.state);

  // Function to pick an image
  Future<void> pickImage(bool isCamera) async {
    final ImagePicker picker = ImagePicker();
    try {
      if (isCamera) {
        // Pick a single image from the camera
        final XFile? image = await picker.pickImage(source: ImageSource.camera);
        if (image != null) {
          // Update the state with the selected image
          state = image;
        }
      } else {
        // Pick a single image from the gallery
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
        );
        if (image != null) {
          // Update the state with the selected image
          state = image;
        }
      }
    } catch (e) {
      // Handle any errors that occur during image picking
      log('Error picking image: $e');
    }
  }

  // Function to remove the selected image
  void removeImage() {
    state = null; // Clear the image selection
  }
}

//multi image
final multiImageProvider = StateNotifierProvider.family
    .autoDispose<MultiImageNotifier, List<XFile>, String>(
      (ref, key) => MultiImageNotifier(),
    );

class MultiImageNotifier extends StateNotifier<List<XFile>> {
  MultiImageNotifier() : super([]);

  // Function to pick images and add to the list
  void pickImages(bool isCamera) async {
    final ImagePicker picker = ImagePicker();
    List<XFile>? images;

    try {
      if (isCamera) {
        // Pick a single image from the camera
        final XFile? image = await picker.pickImage(source: ImageSource.camera);
        if (image != null) {
          images = [image]; // If a single image is taken, wrap it in a list
        }
      } else {
        // Pick multiple images from the gallery
        images = await picker.pickMultiImage();
      }

      // If images are not null, update the state
      if (images != null) {
        state = [...state, ...images]; // Add new images to the existing list
      }
    } catch (e) {
      // print("Error picking images: $e");}
    }

    // Function to remove a specific image from the list
    // void removeImage(XFile image) {
    //   state = state.where((img) => img.path != image.path).toList();
    // }

    // Function to clear all selected images
    // void clearImages() {
    //   state = [];
    // }
  }
}
