import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:restaurant_kitchen_app/features/auth/data/model/user.dart';
import 'package:restaurant_kitchen_app/features/auth/data/repo/login_repo_impl.dart';
import 'package:restaurant_kitchen_app/features/auth/presentation/provider/login_state.dart';
import 'package:restaurant_kitchen_app/main.dart';

final loginProvider = StateNotifierProvider<LoginProvider, LoginState>(
  (ref) => LoginProvider(
    LoginState(
      errMessage: '',
      isError: false,
      isLoad: false,
      isSuccess: false,
      user: ref.watch(box),
    ),
  ),
);

class LoginProvider extends StateNotifier<LoginState> {
  LoginProvider(super.state);

  // user login
  Future<void> userLogin({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(isLoad: true, isError: false, isSuccess: false);
    final res = await LoginRepoImpl().loginUser(
      email: email,
      password: password,
    );
    res.fold(
      (l) {
        state = state.copyWith(isLoad: false, isError: true, errMessage: l);
      },
      (r) {
        state = state.copyWith(
          isLoad: false,
          isError: false,
          isSuccess: true,
          user: [r],
        );
      },
    );
  }

  //----------- user logout ----------
  Future<void> userLogOut() async {
    try {
      final box = Hive.box<User>('userBox');
      await box.clear();
      state = state.copyWith(user: []);
      debugPrint("User successfully logged out");
    } catch (e) {
      debugPrint("Error during logout: $e");
    }
  }
}
