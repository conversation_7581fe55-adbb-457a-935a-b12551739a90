import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:restaurant_kitchen_app/features/auth/data/model/user.dart';
import 'package:restaurant_kitchen_app/features/auth/domain/repo/i_login_repo.dart';
import 'package:restaurant_kitchen_app/network/api.dart';

class LoginRepoImpl extends LoginRepo {
  static Dio dio = Dio();

  @override
  Future<Either<String, User>> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      final res = await dio.post(
        Api.userLogin,
        data: {'email': email, 'password': password},
      );
      final user = User.fromJson(res.data['data']);
      // log('User Data: $user');
      //---save user login info---
      final userInfoData = await Hive.openBox<User>('userBox');
      await userInfoData.clear();
      final loginUserData = User(
        token: user.token,
        userInfo: UserInfo(
          id: user.userInfo.id,
          userRole: user.userInfo.userRole,
          employeeRole: user.userInfo.employeeRole,
        ),
        refreshToken: user.refreshToken,
      );
      await userInfoData.add(loginUserData);
      return Right(loginUserData);
    } on DioException catch (e) {
      if (e.response != null) {
        debugPrint(
          'Error in getting data ${e.response!.statusCode} : ${e.response!.statusMessage}',
        );
        debugPrint('${e.response!.data['message']}');

        return Left('${e.response!.data['message']}');
      } else {
        debugPrint('Error in getting data: ${e.message}');
        return Left('${e.message}');
      }
    }
  }
}
