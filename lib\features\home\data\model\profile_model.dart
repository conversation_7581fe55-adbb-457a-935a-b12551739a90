import 'package:restaurant_kitchen_app/features/auth/data/model/user.dart';

class ProfileModel {
  String id;
  bool isSuperAdmin;
  String userRole;
  String name;
  String address;
  String phone;
  String email;
  String dob;
  String gender;
  String profileImg;
  EmpPosition position;
  EmpShift shift;
  RestaurantInfo restaurantInfo;
  EmpShiftTime shiftTime;
  EmployeeRole employeeRole;

  ProfileModel({
    required this.id,
    required this.isSuperAdmin,
    required this.userRole,
    required this.address,
    required this.phone,
    required this.email,
    required this.dob,
    required this.gender,
    required this.name,
    required this.profileImg,
    required this.position,
    required this.shift,
    required this.restaurantInfo,
    required this.shiftTime,
    required this.employeeRole,
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      id: json['_id'] ?? '',
      isSuperAdmin: json['isSuperAdmin'] ?? false,
      userRole: json['userRole'] ?? '',
      address: json['address'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      dob: json['dob'] ?? '',
      gender: json['gender'] ?? '',
      name: json['name'] ?? '',
      profileImg: json['profileImg'] ?? '',
      position: EmpPosition.fromJson(json['position'] ?? {}),
      shift: EmpShift.fromJson(json['shiftType'] ?? {}),
      restaurantInfo: RestaurantInfo.fromJson(json['restaurant'] ?? {}),
      shiftTime: EmpShiftTime.fromJson(json['time'] ?? {}),
      employeeRole: EmployeeRole.fromJson(json['empRole'] ?? {}),
    );
  }
}

// emp position
class EmpPosition {
  String id;
  String name;
  EmpPosition({required this.name, required this.id});

  factory EmpPosition.fromJson(Map<String, dynamic> json) {
    return EmpPosition(id: json['_id'] ?? '', name: json['name'] ?? '');
  }
}

// emp shift
class EmpShift {
  String id;
  String name;
  EmpShift({required this.name, required this.id});

  factory EmpShift.fromJson(Map<String, dynamic> json) {
    return EmpShift(id: json['_id'] ?? '', name: json['name'] ?? '');
  }
}

// restaurant info
class RestaurantInfo {
  String id;
  String name;

  RestaurantInfo({required this.id, required this.name});

  factory RestaurantInfo.fromJson(Map<String, dynamic> json) {
    return RestaurantInfo(id: json['_id'] ?? '', name: json['name'] ?? '');
  }
}

// emp shiftTime
class EmpShiftTime {
  String from;
  String to;
  EmpShiftTime({required this.from, required this.to});

  factory EmpShiftTime.fromJson(Map<String, dynamic> json) {
    return EmpShiftTime(from: json['from'] ?? '', to: json['to'] ?? '');
  }
}
