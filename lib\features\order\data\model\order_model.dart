import 'package:restaurant_kitchen_app/features/order/data/model/floor_model.dart';

class OrderModel {
  String id;
  String restaurant;
  String orderType;
  num paidAmount;
  String status;
  num pax;
  num discount;
  String date;
  List<OrderItemList> menuItems;
  List<SeatedTable> tables;
  OrderCustomerInfo customerInfo;

  OrderModel({
    required this.id,
    required this.restaurant,
    required this.orderType,
    required this.paidAmount,
    required this.status,
    required this.pax,
    required this.discount,
    required this.date,
    required this.menuItems,
    required this.tables,
    required this.customerInfo,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
    id: json['_id'] ?? '',
    restaurant: json['restaurant'] ?? '',
    orderType: json['orderType'] ?? '',
    paidAmount: json['paidAmount'] ?? 0,
    status: json['status'] ?? '',
    pax: json['pax'] ?? 0,
    discount: json['discount'] ?? 0,
    date: json['date'] ?? '',
    menuItems:
        (json['menuItems']) != null
            ? List<OrderItemList>.from(
              json['menuItems'].map((e) => OrderItemList.fromJson(e)),
            )
            : [],
    tables:
        (json['table']) != null
            ? List<SeatedTable>.from(
              json['table'].map((e) => SeatedTable.fromJson(e)),
            )
            : [],
    customerInfo: OrderCustomerInfo.fromJson(json['customerDetails']),
  );
}

// menu item list of order
class OrderItemList {
  MenuItemInfo item;
  num quantity;
  String note;
  String foodSize;
  String status;
  num sizePrice;
  List<MenuToppings> toppings;

  OrderItemList({
    required this.item,
    required this.quantity,
    required this.note,
    required this.foodSize,
    required this.status,
    required this.sizePrice,
    required this.toppings,
  });

  factory OrderItemList.fromJson(Map<String, dynamic> json) {
    return OrderItemList(
      item: MenuItemInfo.fromJson(json['item']),
      quantity: json['quantity'] ?? 0,
      note: json['note'] ?? '',
      foodSize: json['foodSize'] ?? '',
      status: json['status'] ?? '',
      sizePrice: json['sizePrice'] ?? 0,
      toppings:
          (json['toppings']) != null
              ? List<MenuToppings>.from(
                json['toppings'].map((e) => MenuToppings.fromJson(e)),
              )
              : [],
    );
  }
}

// toppings of order item
class MenuToppings {
  String name;
  num price;

  MenuToppings({required this.name, required this.price});

  factory MenuToppings.fromJson(Map<String, dynamic> json) {
    return MenuToppings(name: json['name'], price: json['price']);
  }
}

// menu item info
class MenuItemInfo {
  String id;
  String name;
  String description;
  List<String> photos;
  List<AvailableFoodSize> foodSize;
  List<AvailableToppings> toppings;
  num price;

  MenuItemInfo({
    required this.id,
    required this.name,
    required this.description,
    required this.photos,
    required this.foodSize,
    required this.price,
    required this.toppings,
  });

  factory MenuItemInfo.fromJson(Map<String, dynamic> json) {
    return MenuItemInfo(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      photos: List<String>.from(json['photos'] ?? []),
      foodSize:
          (json['size']) != null
              ? List<AvailableFoodSize>.from(
                json['size'].map((e) => AvailableFoodSize.fromJson(e)),
              )
              : [],
      toppings:
          (json['toppings']) != null
              ? List<AvailableToppings>.from(
                json['toppings'].map((e) => AvailableToppings.fromJson(e)),
              )
              : [],
      price: json['price'] ?? 0,
    );
  }
}

//available toppings
class AvailableToppings {
  String id;
  String name;
  num price;

  AvailableToppings({
    required this.id,
    required this.name,
    required this.price,
  });

  factory AvailableToppings.fromJson(Map<String, dynamic> json) {
    return AvailableToppings(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      price: json['price'] ?? 0,
    );
  }
  //to json
  Map<String, dynamic> toJson() => {'name': name, 'price': price};
}

// available food size
class AvailableFoodSize {
  String id;
  String size;
  num price;

  AvailableFoodSize({
    required this.id,
    required this.size,
    required this.price,
  });

  factory AvailableFoodSize.fromJson(Map<String, dynamic> json) {
    return AvailableFoodSize(
      id: json['_id'] ?? '',
      size: json['size'] ?? '',
      price: json['price'] ?? 0,
    );
  }
}

//order seated table
class SeatedTable {
  String id;
  String name;
  num capacity;
  String status;
  String waiter;
  FloorModel floor;
  String data;

  SeatedTable({
    required this.id,
    required this.name,
    required this.capacity,
    required this.status,
    required this.waiter,
    required this.floor,
    required this.data,
  });

  factory SeatedTable.fromJson(Map<String, dynamic> json) {
    return SeatedTable(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      capacity: json['capacity'] ?? 0,
      status: json['status'] ?? '',
      waiter: json['waiter'] ?? '',
      floor: FloorModel.fromJson(json['floorPlan']),
      data: json['data'] ?? '',
    );
  }

  // to json
  Map<String, dynamic> toJson() => {'name': name};
}

// order Customer Info
class OrderCustomerInfo {
  String name;
  String phone;

  OrderCustomerInfo({required this.name, required this.phone});

  factory OrderCustomerInfo.fromJson(Map<String, dynamic> json) {
    return OrderCustomerInfo(
      name: json['name'] ?? 'n/a',
      phone: json['phone'] ?? 'n/a',
    );
  }
}
