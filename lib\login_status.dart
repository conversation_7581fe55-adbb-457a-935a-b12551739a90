import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:restaurant_kitchen_app/features/auth/presentation/provider/login_provider.dart';
import 'package:restaurant_kitchen_app/features/auth/presentation/screen/login_screen.dart';
import 'package:restaurant_kitchen_app/features/dashboard/presentation/screen/dashboard.dart';

class LoginStatus extends ConsumerWidget {
  const LoginStatus({super.key});

  @override
  Widget build(BuildContext context, ref) {
    final auth = ref.watch(loginProvider);
    return Scaffold(body: auth.user.isEmpty ? LoginScreen() : Dashboard());
  }
}
