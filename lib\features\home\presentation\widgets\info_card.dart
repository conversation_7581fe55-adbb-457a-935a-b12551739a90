import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restaurant_kitchen_app/features/home/<USER>/widgets/custom_info_card.dart';

class InfoCard extends StatelessWidget {
  const InfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Wrap(
        spacing: 10.w,
        runSpacing: 8.h,
        children: [
          CustomInfoCard(
            onTap: () {},
            title: 'Preparing',
            statusNo: '0',
            imagePath: Icons.access_time_filled,
            color: Colors.green.shade300,
          ),
          CustomInfoCard(
            onTap: () {},
            title: 'Cooking',
            statusNo: '0',
            imagePath: Icons.local_fire_department,
            color: Colors.orange.shade300,
          ),
          CustomInfoCard(
            onTap: () {},
            title: 'Ready to Serve',
            statusNo: '0',
            imagePath: Icons.check_circle,
            color: Colors.blue.shade300,
          ),
          CustomInfoCard(
            onTap: () {},
            title: 'Served',
            statusNo: '0',
            imagePath: Icons.restaurant,
            color: Colors.purple.shade300,
          ),
        ],
      ),
    );
  }
}
