import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/features/profile/presentation/provider/profile_provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProfileHeaderWidget extends ConsumerWidget {
  const ProfileHeaderWidget({super.key});

  String getGreeting() {
    var hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 18) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileState = ref.watch(profileProvider);

    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 4.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            profileState.when(
              data: (data) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      getGreeting(),
                      style: TextStyles.greetingStyle.copyWith(fontSize: 12.sp),
                    ),
                    Text(
                      data.name,
                      style: TextStyles.titleTextStyles.copyWith(
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                );
              },
              error: (error, stackTrace) {
                return Text(
                  error.toString(),
                  style: TextStyles.bodyTextStyles.copyWith(fontSize: 12.sp),
                );
              },
              loading: () {
                return Skeletonizer(
                  effect: ShimmerEffect(
                    duration: const Duration(seconds: 1),
                    baseColor: Colors.grey[300]!,
                    highlightColor: kBrandColor.withValues(alpha: 0.5),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [Text('Loading...'), Text('Loading... ... ...')],
                  ),
                );
              },
            ),
            Container(
              padding: EdgeInsets.all(6.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: kBrandColor.withValues(alpha: 0.85),
              ),
              child: IconButton(
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(),
                style: const ButtonStyle(
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                onPressed: () {},
                icon: Icon(
                  Icons.notifications_none,
                  color: Colors.white,
                  size: 23.r,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
