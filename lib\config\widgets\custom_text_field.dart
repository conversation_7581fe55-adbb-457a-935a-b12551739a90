import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';

class CustomTextField extends StatelessWidget {
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool readOnly;
  final double? fontSize;
  final double? contentPaddingVertical;
  final IconData? icon;
  final void Function()? suffixOnTap;
  final void Function()? onTap;
  final Widget? prefixIcon;
  final int maxLines;
  final int? maxLength;
  final void Function(String)? onChanged;
  final String? labelText;
  final bool? enabled;
  final Color? suffixIconColor;

  const CustomTextField({
    super.key,
    this.hintText,
    required this.controller,
    required this.validator,
    this.textInputAction,
    this.keyboardType,
    this.obscureText = false,
    this.readOnly = false,
    this.icon,
    this.suffixOnTap,
    this.fontSize,
    this.onTap,
    this.prefixIcon,
    this.maxLines = 1,
    this.onChanged,
    this.maxLength,
    this.contentPaddingVertical,
    this.labelText,
    this.enabled = true,
    this.suffixIconColor,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enabled: enabled,
      onTap: onTap,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      cursorHeight: 20.h,
      readOnly: readOnly,
      obscureText: obscureText,
      controller: controller,
      textInputAction: textInputAction,
      keyboardType: keyboardType,
      maxLength: maxLength,
      style: TextStyle(fontSize: fontSize ?? 14.sp),
      cursorColor: Colors.black,
      validator: validator,
      maxLines: maxLines,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: labelText,
        labelStyle: TextStyle(fontSize: 14.sp),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 12,
          vertical: contentPaddingVertical ?? 0,
        ),
        prefixIcon: prefixIcon,
        hintText: hintText,
        hintStyle: TextStyle(color: Colors.grey, fontSize: 14.sp),
        filled: true,
        fillColor: Colors.white,
        suffixIcon: IconButton(
          onPressed: suffixOnTap,
          icon: Icon(icon, color: suffixIconColor ?? Colors.grey),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: Colors.grey.shade400),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: Colors.grey.shade400),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: kBrandColor),
        ),
      ),
    );
  }
}
