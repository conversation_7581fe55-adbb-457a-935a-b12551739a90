import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/sized_box.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProfileLoader extends StatelessWidget {
  const ProfileLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverList.separated(
      separatorBuilder: (context, index) => AppSize.v10,
      itemCount: 8,
      itemBuilder: (context, index) {
        return Skeletonizer(
          effect: ShimmerEffect(
            duration: const Duration(seconds: 1),
            baseColor: Colors.grey[300]!,
            highlightColor: kBrandColor.withValues(alpha: 0.5),
          ),
          child: Padding(
            padding: EdgeInsets.fromLTRB(16.w, 4.h, 16.w, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Loading... ... ... ...'),
                Text('Loading... ... ... ... ... ... ... ... '),
              ],
            ),
          ),
        );
      },
    );
  }
}
