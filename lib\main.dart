import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/features/auth/data/model/user.dart';
import 'package:restaurant_kitchen_app/login_status.dart';

final box = Provider<List<User>>((ref) => []);

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(UserInfoAdapter());
  Hive.registerAdapter(EmployeeRoleAdapter());
  final userData = await Hive.openBox<User>('userBox');

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: kBrandColor,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
    ),
  );

  runApp(
    ProviderScope(
      overrides: [box.overrideWithValue(userData.values.toList())],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: (context, child) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            fontFamily: FontStyles.publicSans,
            scaffoldBackgroundColor: Colors.white,
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 40),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                // backgroundColor: const Color(kBrandColor),
              ),
            ),
            colorScheme: const ColorScheme.light(
              primary: kBrandColor,
              surface: Colors.white,
            ),
            appBarTheme: AppBarTheme(
              backgroundColor: kBrandColor,
              elevation: 0,
              surfaceTintColor: Colors.transparent,
              centerTitle: true,
              titleTextStyle: TextStyles.bodyTextStyles,
              iconTheme: IconThemeData(color: Colors.black),
            ),
          ),
          home: LoginStatus(),
        );
      },
    );
  }
}
