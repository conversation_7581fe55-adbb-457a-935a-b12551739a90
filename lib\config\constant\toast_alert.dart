import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';

class Toasts {
  static showFailure(BuildContext context, String message) {
    Fluttertoast.cancel();
    Fluttertoast.showToast(
      msg: message,
      gravity: ToastGravity.BOTTOM,
      toastLength: Toast.LENGTH_SHORT,
      backgroundColor: const Color.fromARGB(255, 220, 54, 101),
      textColor: Colors.white,
      fontSize: 16.sp,
    );
  }

  static showSuccess(BuildContext context, String message) {
    Fluttertoast.cancel();
    Fluttertoast.showToast(
      msg: message,
      gravity: ToastGravity.BOTTOM,
      toastLength: Toast.LENGTH_SHORT,
      backgroundColor: kBrandColor,
      textColor: Colors.white,
      fontSize: 16.sp,
    );
  }
}
