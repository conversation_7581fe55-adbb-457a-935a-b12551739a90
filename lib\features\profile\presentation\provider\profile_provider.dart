import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:restaurant_kitchen_app/features/auth/presentation/provider/login_provider.dart';
import 'package:restaurant_kitchen_app/features/home/<USER>/model/profile_model.dart';
import 'package:restaurant_kitchen_app/network/api.dart';

final profileProvider = FutureProvider((ref) {
  final auth = ref.watch(loginProvider);
  return ProfileProvider.getProfile(token: auth.user[0].token);
});

class ProfileProvider {
  // get user profile
  static Future<ProfileModel> getProfile({required String token}) async {
    try {
      final res = await Dio().get(
        Api.userProfile,
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );
      final data = ProfileModel.fromJson(res.data['data']);
      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        debugPrint(
          'Error in getting profile data ${e.response!.statusCode} : ${e.response!.statusMessage}',
        );
        throw ('${e.response!.data['message']}');
      } else {
        debugPrint('Error in getting profile data: ${e.message}');
      }
      throw ('${e.message}');
    }
  }
}
