import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/sized_box.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/features/order/presentation/provider/order_provider.dart';
import 'package:restaurant_kitchen_app/features/order/presentation/widgets/order_card.dart';
import 'package:tuple/tuple.dart';

class OrderScreen extends ConsumerWidget {
  const OrderScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String date = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final orderState = ref.watch(orderListPorvider(Tuple2('inprogress', date)));
    return Scaffold(
      appBar: AppBar(
        title: Text('Orders'),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: kBrandColor,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.dark,
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              '${orderState.orderList.length} : Active Orders',
              style: TextStyles.bodyTextStyles.copyWith(
                fontSize: 12.sp,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      body:
          orderState.isError
              ? Column(
                children: [
                  Text(orderState.errMessage, style: TextStyles.bodyTextStyles),
                  IconButton(
                    onPressed: () {
                      ref.invalidate(
                        orderListPorvider(Tuple2('inprogress', date)),
                      );
                    },
                    icon: Icon(Icons.refresh),
                  ),
                ],
              )
              : RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(orderListPorvider(Tuple2('inprogress', date)));
                  return Future.delayed(const Duration(seconds: 1));
                },
                child: ListView.separated(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 10.h,
                  ),
                  separatorBuilder: (context, index) => AppSize.v10,
                  itemCount: orderState.orderList.length,
                  itemBuilder: (context, index) {
                    final order = orderState.orderList[index];
                    final tables = order.tables.map((e) => e.name).toList();
                    return OrderCard(order: order, tables: tables);
                  },
                ),
              ),
    );
  }
}
