import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:form_validation/form_validation.dart';
import 'package:restaurant_kitchen_app/config/constant/sized_box.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/config/constant/toast_alert.dart';
import 'package:restaurant_kitchen_app/config/widgets/custom_elevatedbutton.dart';
import 'package:restaurant_kitchen_app/config/widgets/custom_text_field.dart';
import 'package:restaurant_kitchen_app/features/auth/presentation/provider/login_provider.dart';

final obscureTextProvider = StateProvider<bool>((ref) => true);

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();

  final _emailController = TextEditingController();

  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    ref.listen(loginProvider, (previous, next) async {
      if (next.isError) {
        Toasts.showFailure(context, next.errMessage);
      } else if (next.isSuccess) {
        Toasts.showSuccess(context, 'Login Successful');
      }
    });
    final loginState = ref.watch(loginProvider);
    final obscureText = ref.watch(obscureTextProvider);
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Form(
          key: _formKey,
          child: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Login',
                  style: TextStyles.titleTextStyles.copyWith(fontSize: 18.sp),
                ),
                Text(
                  'Please enter your credentials to login',
                  style: TextStyles.latoTextStyles.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
                AppSize.v20,
                Text('Email', style: TextStyles.textFiedlLabelTextStyles),
                AppSize.v6,
                CustomTextField(
                  controller: _emailController,
                  hintText: 'Email',
                  validator: (val) {
                    final validator = Validator(
                      validators: [
                        const RequiredValidator(),
                        const EmailValidator(),
                      ],
                    );
                    return validator.validate(label: 'Email', value: val);
                  },
                ),
                AppSize.v12,
                Text('Password', style: TextStyles.textFiedlLabelTextStyles),
                AppSize.v6,
                CustomTextField(
                  controller: _passwordController,
                  hintText: 'Password',
                  obscureText: obscureText,
                  icon: obscureText ? Icons.visibility_off : Icons.visibility,
                  suffixOnTap: () {
                    ref.read(obscureTextProvider.notifier).state = !obscureText;
                  },
                  validator: (val) {
                    final validator = Validator(
                      validators: [
                        const RequiredValidator(),
                        const MinLengthValidator(length: 6),
                      ],
                    );
                    return validator.validate(label: 'Password', value: val);
                  },
                ),
                AppSize.v24,
                loginState.isLoad
                    ? Center(child: const CircularProgressIndicator())
                    : CustomElevatedButton(
                      text: 'Login',
                      onPressed: () {
                        FocusScope.of(context).unfocus();
                        _formKey.currentState!.save();
                        if (_formKey.currentState!.validate()) {
                          ref
                              .read(loginProvider.notifier)
                              .userLogin(
                                email: _emailController.text.trim(),
                                password: _passwordController.text.trim(),
                              );
                        }
                      },
                    ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
