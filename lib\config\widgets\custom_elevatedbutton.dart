import 'package:flutter/material.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';

class CustomElevatedButton extends StatelessWidget {
  final String text;
  final double fontSize;
  final FontWeight fontWeight;
  final Color? bgColor;
  final VoidCallback? onPressed;

  const CustomElevatedButton({
    super.key,
    required this.text,
    this.bgColor,
    this.onPressed,
    this.fontSize = 14,
    this.fontWeight = FontWeight.w700,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(backgroundColor: bgColor ?? kBrandColor),
      child: Text(
        text,
        style: TextStyles.latoTextStyles.copyWith(
          color: Colors.white,
          fontSize: fontSize,
          fontWeight: fontWeight,
        ),

        // style: TextStyle(
        //   color: Colors.white,
        //   fontSize: fontSize,
        //   fontWeight: fontWeight,
        // ),
      ),
    );
  }
}
