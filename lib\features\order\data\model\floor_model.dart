class FloorModel {
  String id;
  String floor;
  bool isActive;
  String restaurant;

  FloorModel({
    required this.id,
    required this.floor,
    required this.isActive,
    required this.restaurant,
  });

  factory FloorModel.fromJson(Map<String, dynamic> json) => FloorModel(
        id: json["_id"] ?? '',
        floor: json["name"] ?? '',
        isActive: json["isActive"] ?? false,
        restaurant: json["restaurant"] ?? '',
      );
}
