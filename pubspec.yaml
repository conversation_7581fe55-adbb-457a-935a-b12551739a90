name: restaurant_kitchen_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.6.1
  dartz: ^0.10.1
  flutter_screenutil: ^5.9.3
  dio: ^5.8.0+1
  form_validation: ^3.2.0
  fluttertoast: ^8.2.12
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  skeletonizer: any
  image_picker: ^1.1.2
  intl: ^0.20.2
  tuple: ^2.0.2
  socket_io_client: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.5.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  fonts:
    - family: Poppins
      fonts:
        - asset: fonts/poppins/Poppins-Regular.ttf
          weight: 400
        - asset: fonts/poppins/Poppins-Light.ttf
          weight: 300
        - asset: fonts/poppins/Poppins-Medium.ttf
          weight: 500
        - asset: fonts/poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: fonts/poppins/Poppins-Bold.ttf
          weight: 700    
    - family: PublicSans-Regular
      fonts:
        - asset: fonts/public_sans/PublicSans-Regular.otf
    - family: PublicSans-Medium
      fonts:
        - asset: fonts/public_sans/PublicSans-Medium.otf

    - family: Lato
      fonts:
        - asset: fonts/lato/Lato-Regular.ttf
          weight: 400
        - asset: fonts/lato/Lato-Black.ttf
          weight: 900
        - asset: fonts/lato/Lato-Bold.ttf
          weight: 700
        - asset: fonts/lato/Lato-Bold.ttf
          weight: 600
        - asset: fonts/lato/Lato-Thin.ttf
          weight: 300

    - family: OpenSans
      fonts:
        - asset: fonts/OpenSans/OpenSans-Bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
