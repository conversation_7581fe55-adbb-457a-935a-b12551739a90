import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restaurant_kitchen_app/config/constant/color_constants.dart';
import 'package:restaurant_kitchen_app/config/constant/sized_box.dart';
import 'package:restaurant_kitchen_app/config/constant/text_styles.dart';
import 'package:restaurant_kitchen_app/config/widgets/custom_elevatedbutton.dart';
import 'package:restaurant_kitchen_app/features/auth/presentation/provider/login_provider.dart';
import 'package:restaurant_kitchen_app/features/profile/presentation/provider/profile_provider.dart';
import 'package:restaurant_kitchen_app/features/profile/presentation/widgets/profile_loader.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileState = ref.watch(profileProvider);
    ref.watch(loginProvider);

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(profileProvider);
          return Future.delayed(const Duration(seconds: 1));
        },
        child: CustomScrollView(
          key: const PageStorageKey('profile_screen'),
          slivers: [
            SliverLayoutBuilder(
              builder: (context, constraints) {
                final isCollapsed = constraints.scrollOffset > 150;

                return SliverAppBar(
                  backgroundColor: kBrandColor,
                  pinned: true,
                  expandedHeight: 210,
                  systemOverlayStyle: const SystemUiOverlayStyle(
                    statusBarColor: Colors.transparent,
                    statusBarIconBrightness: Brightness.dark,
                    statusBarBrightness: Brightness.dark,
                  ),
                  automaticallyImplyLeading: false,
                  leading: AnimatedOpacity(
                    opacity: isCollapsed ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child:
                        profileState.whenOrNull(
                          data: (data) {
                            return Transform.scale(
                              scale: 0.7,
                              child: CircleAvatar(
                                backgroundImage: NetworkImage(data.profileImg),
                              ),
                            );
                          },
                        ) ??
                        const SizedBox.shrink(),
                  ),
                  title: AnimatedOpacity(
                    opacity: isCollapsed ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Text('Profile', style: TextStyles.titleTextStyles),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    background:
                        profileState.whenOrNull(
                          data: (data) {
                            return Image.network(
                              data.profileImg,
                              fit: BoxFit.cover,
                            );
                          },
                        ) ??
                        const Center(child: CircularProgressIndicator()),
                  ),
                );
              },
            ),
            profileState.isRefreshing
                ? ProfileLoader()
                : profileState.when(
                  data: (data) {
                    return SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 16.h,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Name',
                              style: TextStyles.textFiedlLabelTextStyles,
                            ),
                            AppSize.v2,
                            Text(data.name, style: TextStyles.bodyTextStyles),
                            AppSize.v10,
                            Text(
                              'Email',
                              style: TextStyles.textFiedlLabelTextStyles,
                            ),
                            AppSize.v2,
                            Text(data.email, style: TextStyles.bodyTextStyles),
                            AppSize.v10,
                            Text(
                              'Phone',
                              style: TextStyles.textFiedlLabelTextStyles,
                            ),
                            AppSize.v2,
                            Text(data.phone, style: TextStyles.bodyTextStyles),
                            AppSize.v10,
                            Text(
                              'Address',
                              style: TextStyles.textFiedlLabelTextStyles,
                            ),
                            AppSize.v2,
                            Text(
                              data.address,
                              style: TextStyles.bodyTextStyles,
                            ),
                            AppSize.v10,
                            Text(
                              'Restaurant',
                              style: TextStyles.textFiedlLabelTextStyles,
                            ),
                            AppSize.v2,
                            Text(
                              data.restaurantInfo.name,
                              style: TextStyles.bodyTextStyles,
                            ),
                            AppSize.v10,
                            Text(
                              'Employee Role',
                              style: TextStyles.textFiedlLabelTextStyles,
                            ),
                            AppSize.v2,
                            Text(
                              data.employeeRole.name,
                              style: TextStyles.bodyTextStyles,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  error: (error, stackTrace) {
                    return SliverToBoxAdapter(
                      child: Center(child: Text(error.toString())),
                    );
                  },
                  loading: () {
                    return SliverToBoxAdapter(
                      child: Center(child: CircularProgressIndicator()),
                    );
                  },
                ),
            SliverToBoxAdapter(child: AppSize.v40),
            SliverToBoxAdapter(child: AppSize.v40),
            SliverToBoxAdapter(child: AppSize.v40),
            SliverToBoxAdapter(child: AppSize.v40),
            SliverToBoxAdapter(child: AppSize.v40),
            SliverToBoxAdapter(child: AppSize.v40),
            SliverToBoxAdapter(child: AppSize.v40),
            SliverToBoxAdapter(child: AppSize.v40),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: CustomElevatedButton(
          bgColor: Colors.red,
          text: 'Logout',
          onPressed: () {
            ref.read(loginProvider.notifier).userLogOut();
          },
        ),
      ),
    );
  }
}
