import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:restaurant_kitchen_app/config/constant/sized_box.dart';
import 'package:restaurant_kitchen_app/features/home/<USER>/widgets/info_card.dart';
import 'package:restaurant_kitchen_app/features/home/<USER>/widgets/order_list_home.dart';
import 'package:restaurant_kitchen_app/features/home/<USER>/widgets/profile_header_widget.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProfileHeaderWidget(),
        AppSize.v4,
        InfoCard(),
        OrderListHome(),
      ],
    );
  }
}
