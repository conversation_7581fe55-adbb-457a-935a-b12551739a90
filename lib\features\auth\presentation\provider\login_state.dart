import 'package:restaurant_kitchen_app/features/auth/data/model/user.dart';

class LoginState {
  final bool isError;
  final String errMessage;
  final bool isSuccess;
  final bool isLoad;
  final List<User> user;

  LoginState({
    required this.errMessage,
    required this.isError,
    required this.isLoad,
    required this.isSuccess,
    required this.user,
  });

  LoginState copyWith({
    bool? isError,
    String? errMessage,
    bool? isSuccess,
    bool? isLoad,
    List<User>? user,
  }) {
    return LoginState(
      errMessage: errMessage ?? this.errMessage,
      isError: isError ?? this.isError,
      isLoad: isLoad ?? this.isLoad,
      isSuccess: isSuccess ?? this.isSuccess,
      user: user ?? this.user,
    );
  }
}
